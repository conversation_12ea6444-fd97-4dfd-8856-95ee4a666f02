import React, { useState, useRef } from 'react';
import { Upload, X, Star, StarOff, Image as ImageIcon } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import { imageService, ProductImage } from '@/services/imageService';

interface ImageUploadProps {
  productId?: string;
  images: ProductImage[];
  onImagesChange: (images: ProductImage[]) => void;
  maxImages?: number;
  disabled?: boolean;
}

export function ImageUpload({ 
  productId, 
  images, 
  onImagesChange, 
  maxImages = 10,
  disabled = false 
}: ImageUploadProps) {
  const [uploading, setUploading] = useState(false);
  const [dragOver, setDragOver] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { toast } = useToast();

  const handleFileSelect = async (files: FileList | null) => {
    if (!files || files.length === 0) return;

    const fileArray = Array.from(files);
    
    // Validate files
    const validFiles: File[] = [];
    for (const file of fileArray) {
      const validation = imageService.validateImageFile(file);
      if (!validation.valid) {
        toast({
          title: "Invalid file",
          description: `${file.name}: ${validation.error}`,
          variant: "destructive",
        });
        continue;
      }
      validFiles.push(file);
    }

    if (validFiles.length === 0) return;

    // Check if we exceed max images
    if (images.length + validFiles.length > maxImages) {
      toast({
        title: "Too many images",
        description: `Maximum ${maxImages} images allowed`,
        variant: "destructive",
      });
      return;
    }

    setUploading(true);

    try {
      if (productId) {
        // Upload to storage if product exists
        const { data: urls, error } = await imageService.uploadMultipleImages(validFiles, productId);
        if (error) throw error;

        const newImages: ProductImage[] = urls!.map((url, index) => ({
          url,
          is_primary: images.length === 0 && index === 0, // First image is primary if no images exist
          alt_text: validFiles[index].name,
          file_name: validFiles[index].name,
          file_size: validFiles[index].size,
          uploaded_at: new Date().toISOString()
        }));

        onImagesChange([...images, ...newImages]);
      } else {
        // For new products, create temporary URLs
        const newImages: ProductImage[] = await Promise.all(
          validFiles.map(async (file, index) => ({
            url: URL.createObjectURL(file),
            is_primary: images.length === 0 && index === 0,
            alt_text: file.name,
            file_name: file.name,
            file_size: file.size,
            uploaded_at: new Date().toISOString()
          }))
        );

        onImagesChange([...images, ...newImages]);
      }

      toast({
        title: "Success",
        description: `${validFiles.length} image(s) uploaded successfully`,
      });
    } catch (error: any) {
      console.error('Upload error:', error);
      toast({
        title: "Upload failed",
        description: error.message || "Failed to upload images",
        variant: "destructive",
      });
    } finally {
      setUploading(false);
    }
  };

  const handleRemoveImage = async (index: number) => {
    const imageToRemove = images[index];
    
    try {
      // Delete from storage if it's a real URL (not blob)
      if (productId && !imageToRemove.url.startsWith('blob:')) {
        await imageService.deleteImage(imageToRemove.url);
      }

      const updatedImages = images.filter((_, i) => i !== index);
      
      // If we removed the primary image, make the first remaining image primary
      if (imageToRemove.is_primary && updatedImages.length > 0) {
        updatedImages[0].is_primary = true;
      }

      onImagesChange(updatedImages);

      toast({
        title: "Success",
        description: "Image removed successfully",
      });
    } catch (error: any) {
      console.error('Remove error:', error);
      toast({
        title: "Error",
        description: "Failed to remove image",
        variant: "destructive",
      });
    }
  };

  const handleSetPrimary = (index: number) => {
    const updatedImages = images.map((img, i) => ({
      ...img,
      is_primary: i === index
    }));
    onImagesChange(updatedImages);
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(false);
    handleFileSelect(e.dataTransfer.files);
  };

  return (
    <div className="space-y-4">
      {/* Upload Area */}
      <Card 
        className={`border-2 border-dashed transition-colors ${
          dragOver ? 'border-primary bg-primary/5' : 'border-muted-foreground/25'
        } ${disabled ? 'opacity-50 pointer-events-none' : ''}`}
      >
        <CardContent 
          className="p-6 text-center cursor-pointer"
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
          onClick={() => fileInputRef.current?.click()}
        >
          <div className="flex flex-col items-center gap-2">
            <Upload className="h-8 w-8 text-muted-foreground" />
            <div>
              <p className="text-sm font-medium">
                {uploading ? 'Uploading...' : 'Click to upload or drag and drop'}
              </p>
              <p className="text-xs text-muted-foreground">
                PNG, JPG, WebP up to 5MB ({images.length}/{maxImages} images)
              </p>
            </div>
          </div>
          <input
            ref={fileInputRef}
            type="file"
            accept="image/*"
            multiple
            className="hidden"
            onChange={(e) => handleFileSelect(e.target.files)}
            disabled={uploading || disabled}
          />
        </CardContent>
      </Card>

      {/* Image Grid */}
      {images.length > 0 && (
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
          {images.map((image, index) => (
            <Card key={index} className="relative group">
              <CardContent className="p-2">
                <div className="relative aspect-square">
                  <img
                    src={image.url}
                    alt={image.alt_text || `Product image ${index + 1}`}
                    className="w-full h-full object-cover rounded"
                  />
                  
                  {/* Primary Badge */}
                  {image.is_primary && (
                    <Badge className="absolute top-1 left-1 text-xs">
                      Primary
                    </Badge>
                  )}

                  {/* Action Buttons */}
                  <div className="absolute top-1 right-1 flex gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
                    <Button
                      size="sm"
                      variant={image.is_primary ? "default" : "secondary"}
                      className="h-6 w-6 p-0"
                      onClick={() => handleSetPrimary(index)}
                      title={image.is_primary ? "Primary image" : "Set as primary"}
                    >
                      {image.is_primary ? (
                        <Star className="h-3 w-3" />
                      ) : (
                        <StarOff className="h-3 w-3" />
                      )}
                    </Button>
                    <Button
                      size="sm"
                      variant="destructive"
                      className="h-6 w-6 p-0"
                      onClick={() => handleRemoveImage(index)}
                      title="Remove image"
                    >
                      <X className="h-3 w-3" />
                    </Button>
                  </div>
                </div>
                
                {/* Image Info */}
                <div className="mt-2 text-xs text-muted-foreground">
                  <p className="truncate">{image.file_name}</p>
                  {image.file_size && (
                    <p>{(image.file_size / 1024 / 1024).toFixed(2)} MB</p>
                  )}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Empty State */}
      {images.length === 0 && (
        <div className="text-center py-8 text-muted-foreground">
          <ImageIcon className="h-12 w-12 mx-auto mb-2 opacity-50" />
          <p>No images uploaded yet</p>
        </div>
      )}
    </div>
  );
}
