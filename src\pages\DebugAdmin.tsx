import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { productService } from '@/services/productService';
import { orderService } from '@/services/orderService';
import { Layout } from '@/components/layout/Layout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';

export default function DebugAdmin() {
  const { user, profile, loading: authLoading } = useAuth();
  const [products, setProducts] = useState<any[]>([]);
  const [orders, setOrders] = useState<any[]>([]);
  const [productError, setProductError] = useState<string | null>(null);
  const [orderError, setOrderError] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);

  const testProductFetch = async () => {
    try {
      setLoading(true);
      setProductError(null);
      console.log('Testing product fetch...');
      const result = await productService.getAllProducts();
      console.log('Product result:', result);
      if (result.error) {
        setProductError(JSON.stringify(result.error));
      } else {
        setProducts(result.data || []);
      }
    } catch (error) {
      console.error('Product fetch error:', error);
      setProductError(error instanceof Error ? error.message : 'Unknown error');
    } finally {
      setLoading(false);
    }
  };

  const testOrderFetch = async () => {
    try {
      setLoading(true);
      setOrderError(null);
      console.log('Testing order fetch...');
      const result = await orderService.getAllOrders();
      console.log('Order result:', result);
      if (result.error) {
        setOrderError(JSON.stringify(result.error));
      } else {
        setOrders(result.data || []);
      }
    } catch (error) {
      console.error('Order fetch error:', error);
      setOrderError(error instanceof Error ? error.message : 'Unknown error');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (!authLoading && profile) {
      testProductFetch();
      testOrderFetch();
    }
  }, [authLoading, profile]);

  return (
    <Layout>
      <div className="container py-8">
        <h1 className="text-3xl font-bold mb-8">Admin Debug Page</h1>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <CardTitle>Authentication Status</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <p><strong>Auth Loading:</strong> {authLoading ? 'Yes' : 'No'}</p>
                <p><strong>User ID:</strong> {user?.id || 'Not logged in'}</p>
                <p><strong>User Email:</strong> {user?.email || 'N/A'}</p>
                <p><strong>Profile:</strong> {profile ? 'Loaded' : 'Not loaded'}</p>
                <p><strong>Is Admin:</strong> {profile?.is_admin ? 'Yes' : 'No'}</p>
                <p><strong>Profile Email:</strong> {profile?.email || 'N/A'}</p>
                <p><strong>Profile Name:</strong> {profile?.full_name || 'N/A'}</p>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Product Data</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <Button onClick={testProductFetch} disabled={loading}>
                  {loading ? 'Loading...' : 'Test Product Fetch'}
                </Button>
                <p><strong>Products Count:</strong> {products.length}</p>
                {productError && (
                  <div className="text-red-500">
                    <strong>Error:</strong> {productError}
                  </div>
                )}
                {products.length > 0 && (
                  <div>
                    <strong>First Product:</strong>
                    <pre className="text-xs bg-gray-100 p-2 rounded">
                      {JSON.stringify(products[0], null, 2)}
                    </pre>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Order Data</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <Button onClick={testOrderFetch} disabled={loading}>
                  {loading ? 'Loading...' : 'Test Order Fetch'}
                </Button>
                <p><strong>Orders Count:</strong> {orders.length}</p>
                {orderError && (
                  <div className="text-red-500">
                    <strong>Error:</strong> {orderError}
                  </div>
                )}
                {orders.length > 0 && (
                  <div>
                    <strong>First Order:</strong>
                    <pre className="text-xs bg-gray-100 p-2 rounded">
                      {JSON.stringify(orders[0], null, 2)}
                    </pre>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Navigation Test</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <Button asChild>
                  <a href="/admin">Go to Admin Dashboard</a>
                </Button>
                <Button asChild>
                  <a href="/admin/products">Go to Admin Products</a>
                </Button>
                <Button asChild>
                  <a href="/admin/orders">Go to Admin Orders</a>
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </Layout>
  );
}
