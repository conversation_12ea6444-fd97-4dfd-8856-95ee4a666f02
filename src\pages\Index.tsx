
import React, { useEffect, useState } from 'react';
import { Link } from 'react-router-dom';
import { ArrowRight, ShoppingCart } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { ProductGrid } from '@/components/products/ProductGrid';
import { supabase } from '@/lib/supabase';
import { Layout } from '@/components/layout/Layout';

interface Product {
  id: string;
  name: string;
  description?: string;
  price: number;
  category: string;
  image_url?: string;
}

export default function Index() {
  const [featuredProducts, setFeaturedProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchFeaturedProducts();
  }, []);

  const fetchFeaturedProducts = async () => {
    try {
      const { data, error } = await supabase
        .from('products')
        .select('*')
        .eq('is_active', true)
        .order('created_at', { ascending: false })
        .limit(4);

      if (error) throw error;
      setFeaturedProducts(data || []);
    } catch (error) {
      console.error('Error fetching products:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Layout>
      {/* Hero Section */}
      <section className="relative py-20 md:py-32 overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-primary/10 via-accent/10 to-secondary/10" />
        <div className="container relative">
          <div className="max-w-4xl mx-auto text-center space-y-8">
            <h1 className="text-4xl md:text-6xl font-bold leading-tight">
              Welcome to{' '}
              <span className="gradient-text">ENZO SHOP</span>
            </h1>
            <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
              Discover premium digital products that will accelerate your learning journey.
              From comprehensive guides to complete course packages.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" className="hover-glow" asChild>
                <Link to="/products">
                  <ShoppingCart className="mr-2 h-5 w-5" />
                  Browse Products
                </Link>
              </Button>
              <Button size="lg" variant="outline" className="hover-glow" asChild>
                <Link to="/about">
                  Learn More
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Featured Products */}
      <section className="py-16 md:py-24">
        <div className="container">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">
              Featured Products
            </h2>
            <p className="text-muted-foreground max-w-2xl mx-auto">
              Discover our most popular digital products, carefully curated for modern learners
            </p>
          </div>
          <ProductGrid products={featuredProducts} loading={loading} />
          {!loading && featuredProducts.length > 0 && (
            <div className="text-center mt-12">
              <Button variant="outline" className="hover-glow" asChild>
                <Link to="/products">
                  View All Products
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
            </div>
          )}
        </div>
      </section>

      {/* Categories */}
      <section className="py-16 md:py-24 bg-muted/30">
        <div className="container">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">
              Shop by Category
            </h2>
            <p className="text-muted-foreground max-w-2xl mx-auto">
              Find exactly what you're looking for in our organized categories
            </p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            {[
              {
                category: 'Electronics',
                title: 'Electronics',
                description: 'Premium electronics products and gadgets',
                icon: '📱',
                href: '/products?category=Electronics',
              },
              {
                category: 'PDF',
                title: 'PDF Guides',
                description: 'Comprehensive guides and tutorials in PDF format',
                icon: '📄',
                href: '/products?category=PDF',
              },
              {
                category: 'Book',
                title: 'Digital Books',
                description: 'In-depth books on various topics',
                icon: '📚',
                href: '/products?category=Book',
              },
              {
                category: 'Package',
                title: 'Course Packages',
                description: 'Complete learning packages with multiple resources',
                icon: '📦',
                href: '/products?category=Package',
              },
            ].map((category) => (
              <Link
                key={category.category}
                to={category.href}
                className="group"
              >
                <div className="glass-effect p-8 rounded-lg hover-glow transition-all duration-300">
                  <div className="text-4xl mb-4">{category.icon}</div>
                  <h3 className="text-xl font-semibold mb-2 group-hover:text-primary transition-colors">
                    {category.title}
                  </h3>
                  <p className="text-muted-foreground">
                    {category.description}
                  </p>
                </div>
              </Link>
            ))}
          </div>
        </div>
      </section>
    </Layout>
  );
}
