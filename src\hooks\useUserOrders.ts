import { useState, useEffect } from 'react';
import { OrderWithItems } from '@/types/order';
import { orderService } from '@/services/orderService';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/contexts/AuthContext';

export const useUserOrders = () => {
  const [orders, setOrders] = useState<OrderWithItems[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { toast } = useToast();
  const { user } = useAuth();

  const fetchUserOrders = async () => {
    if (!user) {
      setOrders([]);
      return;
    }

    try {
      setLoading(true);
      setError(null);
      console.log('useUserOrders: Fetching orders for user:', user.id);
      
      const { data, error } = await orderService.getUserOrders(user.id);

      if (error) {
        console.error('useUserOrders: Error from orderService:', error);
        throw error;
      }
      
      console.log('useUserOrders: Orders fetched:', data?.length || 0);
      setOrders(data || []);
    } catch (err: any) {
      console.error('useUserOrders: Fetch failed:', err);
      setError(err.message || 'Failed to fetch orders');
      toast({
        title: "Error",
        description: "Failed to load your orders",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchUserOrders();
  }, [user]);

  const refreshOrders = () => {
    fetchUserOrders();
  };

  return {
    orders,
    loading,
    error,
    refreshOrders,
  };
};
