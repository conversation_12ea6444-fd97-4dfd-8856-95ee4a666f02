services:
  - type: web
    name: enzo-shop
    env: static
    buildCommand: npm install && npm run build
    staticPublishPath: ./dist
    routes:
      - type: rewrite
        source: /*
        destination: /index.html
    envVars:
      - key: VITE_SUPABASE_URL
        value: https://olnhzuxguxjwcbsxytju.supabase.co
      - key: VITE_SUPABASE_ANON_KEY
        value: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im9sbmh6dXhndXhqd2Nic3h5dGp1Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg2MTc3MDEsImV4cCI6MjA2NDE5MzcwMX0.zKcLCo1k2G4pwOyV2lnfKTkPeKdMQV1W1SKd81hWTxQ
