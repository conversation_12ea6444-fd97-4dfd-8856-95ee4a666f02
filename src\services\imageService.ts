import { supabase } from '@/integrations/supabase/client';

export interface ProductImage {
  id?: string;
  url: string;
  is_primary: boolean;
  alt_text?: string;
  file_name?: string;
  file_size?: number;
  uploaded_at?: string;
}

export const imageService = {
  /**
   * Upload a single image to Supabase Storage
   */
  async uploadImage(file: File, productId: string): Promise<{ data: string | null; error: any }> {
    try {
      const fileExt = file.name.split('.').pop();
      const fileName = `${productId}/${Date.now()}-${Math.random().toString(36).substring(2)}.${fileExt}`;
      
      const { data, error } = await supabase.storage
        .from('product-images')
        .upload(fileName, file, {
          cacheControl: '3600',
          upsert: false
        });

      if (error) throw error;

      // Get public URL
      const { data: { publicUrl } } = supabase.storage
        .from('product-images')
        .getPublicUrl(fileName);

      return { data: publicUrl, error: null };
    } catch (error) {
      console.error('Error uploading image:', error);
      return { data: null, error };
    }
  },

  /**
   * Upload multiple images
   */
  async uploadMultipleImages(files: File[], productId: string): Promise<{ data: string[] | null; error: any }> {
    try {
      const uploadPromises = files.map(file => this.uploadImage(file, productId));
      const results = await Promise.all(uploadPromises);
      
      const errors = results.filter(result => result.error);
      if (errors.length > 0) {
        throw new Error(`Failed to upload ${errors.length} images`);
      }

      const urls = results.map(result => result.data).filter(Boolean) as string[];
      return { data: urls, error: null };
    } catch (error) {
      console.error('Error uploading multiple images:', error);
      return { data: null, error };
    }
  },

  /**
   * Delete an image from storage
   */
  async deleteImage(imageUrl: string): Promise<{ error: any }> {
    try {
      // Extract file path from URL
      const urlParts = imageUrl.split('/');
      const bucketIndex = urlParts.findIndex(part => part === 'product-images');
      if (bucketIndex === -1) {
        throw new Error('Invalid image URL');
      }
      
      const filePath = urlParts.slice(bucketIndex + 1).join('/');
      
      const { error } = await supabase.storage
        .from('product-images')
        .remove([filePath]);

      return { error };
    } catch (error) {
      console.error('Error deleting image:', error);
      return { error };
    }
  },

  /**
   * Update product images in database
   */
  async updateProductImages(productId: string, images: ProductImage[]): Promise<{ data: any; error: any }> {
    try {
      const { data, error } = await supabase
        .from('products')
        .update({ 
          product_images: images,
          image_url: images.find(img => img.is_primary)?.url || images[0]?.url || null,
          updated_at: new Date().toISOString()
        })
        .eq('id', productId)
        .select()
        .single();

      return { data, error };
    } catch (error) {
      console.error('Error updating product images:', error);
      return { data: null, error };
    }
  },

  /**
   * Get product images
   */
  async getProductImages(productId: string): Promise<{ data: ProductImage[] | null; error: any }> {
    try {
      const { data, error } = await supabase
        .from('products')
        .select('product_images')
        .eq('id', productId)
        .single();

      if (error) throw error;

      return { data: data?.product_images || [], error: null };
    } catch (error) {
      console.error('Error fetching product images:', error);
      return { data: null, error };
    }
  },

  /**
   * Set primary image
   */
  async setPrimaryImage(productId: string, imageUrl: string): Promise<{ data: any; error: any }> {
    try {
      // First get current images
      const { data: currentImages, error: fetchError } = await this.getProductImages(productId);
      if (fetchError) throw fetchError;

      // Update primary status
      const updatedImages = (currentImages || []).map(img => ({
        ...img,
        is_primary: img.url === imageUrl
      }));

      // Update in database
      return await this.updateProductImages(productId, updatedImages);
    } catch (error) {
      console.error('Error setting primary image:', error);
      return { data: null, error };
    }
  },

  /**
   * Validate image file
   */
  validateImageFile(file: File): { valid: boolean; error?: string } {
    const maxSize = 5 * 1024 * 1024; // 5MB
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];

    if (!allowedTypes.includes(file.type)) {
      return { valid: false, error: 'Only JPEG, PNG, and WebP images are allowed' };
    }

    if (file.size > maxSize) {
      return { valid: false, error: 'Image size must be less than 5MB' };
    }

    return { valid: true };
  },

  /**
   * Generate thumbnail URL (if needed for optimization)
   */
  getThumbnailUrl(imageUrl: string, width: number = 300, height: number = 300): string {
    // For now, return original URL. Can be enhanced with image transformation service
    return imageUrl;
  }
};
