
import React from 'react';
import { Package, Calendar, MapPin, CreditCard } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { useAuth } from '@/contexts/AuthContext';
import { Layout } from '@/components/layout/Layout';
import { Navigate, Link } from 'react-router-dom';
import { useUserOrders } from '@/hooks/useUserOrders';

export default function Orders() {
  const { user } = useAuth();
  const { orders, loading, error } = useUserOrders();

  if (!user) {
    return <Navigate to="/login" replace />;
  }

  const getStatusVariant = (status: string) => {
    switch (status) {
      case 'pending': return 'secondary';
      case 'processing': return 'outline';
      case 'shipped': return 'default';
      case 'delivered': return 'default';
      case 'cancelled': return 'destructive';
      default: return 'secondary';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'text-yellow-600';
      case 'processing': return 'text-blue-600';
      case 'shipped': return 'text-purple-600';
      case 'delivered': return 'text-green-600';
      case 'cancelled': return 'text-red-600';
      default: return 'text-gray-600';
    }
  };

  return (
    <Layout>
      <div className="container py-8">
        <div className="flex justify-between items-center mb-8">
          <h1 className="text-3xl font-bold">My Orders</h1>
          <Button asChild>
            <Link to="/products">Continue Shopping</Link>
          </Button>
        </div>

        {loading ? (
          <div className="text-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
            <p>Loading your orders...</p>
          </div>
        ) : error ? (
          <div className="text-center py-12">
            <Package className="h-16 w-16 mx-auto text-red-500 mb-4" />
            <h3 className="text-lg font-semibold mb-2 text-red-600">Error Loading Orders</h3>
            <p className="text-muted-foreground mb-4">{error}</p>
            <Button onClick={() => window.location.reload()}>Try Again</Button>
          </div>
        ) : orders.length === 0 ? (
          <div className="text-center py-12">
            <Package className="h-16 w-16 mx-auto text-muted-foreground mb-4" />
            <h3 className="text-lg font-semibold mb-2">No orders yet</h3>
            <p className="text-muted-foreground mb-6">
              Your order history will appear here once you make your first purchase
            </p>
            <Button asChild>
              <Link to="/products">Start Shopping</Link>
            </Button>
          </div>
        ) : (
          <div className="space-y-6">
            {orders.map((order) => (
              <Card key={order.id} className="glass-effect">
                <CardHeader>
                  <div className="flex justify-between items-start">
                    <div>
                      <CardTitle className="text-lg">
                        Order #{order.id.slice(0, 8).toUpperCase()}
                      </CardTitle>
                      <div className="flex items-center gap-4 text-sm text-muted-foreground mt-1">
                        <div className="flex items-center gap-1">
                          <Calendar className="h-4 w-4" />
                          {order.created_at ? new Date(order.created_at).toLocaleDateString() : 'N/A'}
                        </div>
                        <div className="flex items-center gap-1">
                          <CreditCard className="h-4 w-4" />
                          ${order.total_amount.toFixed(2)}
                        </div>
                      </div>
                    </div>
                    <Badge variant={getStatusVariant(order.status || 'pending')}>
                      {(order.status || 'pending').charAt(0).toUpperCase() + (order.status || 'pending').slice(1)}
                    </Badge>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {/* Order Items */}
                    <div>
                      <h4 className="font-semibold mb-2">Items ({order.order_items?.length || 0})</h4>
                      <div className="space-y-2">
                        {order.order_items?.map((item) => (
                          <div key={item.id} className="flex items-center gap-3 p-2 bg-muted/50 rounded">
                            {item.products?.image_url && (
                              <img
                                src={item.products.image_url}
                                alt={item.products?.name || 'Product'}
                                className="w-12 h-12 object-cover rounded"
                              />
                            )}
                            <div className="flex-1">
                              <p className="font-medium">{item.products?.name || 'Unknown Product'}</p>
                              <p className="text-sm text-muted-foreground">
                                Quantity: {item.quantity} × ${item.price_at_time.toFixed(2)}
                              </p>
                            </div>
                            <div className="text-right">
                              <p className="font-medium">${(item.price_at_time * item.quantity).toFixed(2)}</p>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>

                    {/* Shipping Address */}
                    {order.shipping_address && (
                      <div>
                        <h4 className="font-semibold mb-2 flex items-center gap-2">
                          <MapPin className="h-4 w-4" />
                          Shipping Address
                        </h4>
                        <div className="text-sm text-muted-foreground bg-muted/50 p-3 rounded">
                          <p>{order.customer_name}</p>
                          <p>{order.shipping_address.street}</p>
                          <p>{order.shipping_address.city}, {order.shipping_address.state} {order.shipping_address.zip}</p>
                          <p>{order.shipping_address.country}</p>
                        </div>
                      </div>
                    )}

                    {/* Order Status Timeline */}
                    <div>
                      <h4 className="font-semibold mb-2">Order Status</h4>
                      <div className="flex items-center gap-2">
                        <div className={`w-3 h-3 rounded-full ${order.status === 'pending' ? 'bg-yellow-500' : 'bg-gray-300'}`}></div>
                        <span className={`text-sm ${order.status === 'pending' ? 'font-medium' : 'text-muted-foreground'}`}>Pending</span>
                        <div className="flex-1 h-px bg-gray-300"></div>
                        <div className={`w-3 h-3 rounded-full ${order.status === 'processing' ? 'bg-blue-500' : order.status === 'shipped' || order.status === 'delivered' ? 'bg-green-500' : 'bg-gray-300'}`}></div>
                        <span className={`text-sm ${order.status === 'processing' ? 'font-medium' : 'text-muted-foreground'}`}>Processing</span>
                        <div className="flex-1 h-px bg-gray-300"></div>
                        <div className={`w-3 h-3 rounded-full ${order.status === 'shipped' ? 'bg-purple-500' : order.status === 'delivered' ? 'bg-green-500' : 'bg-gray-300'}`}></div>
                        <span className={`text-sm ${order.status === 'shipped' ? 'font-medium' : 'text-muted-foreground'}`}>Shipped</span>
                        <div className="flex-1 h-px bg-gray-300"></div>
                        <div className={`w-3 h-3 rounded-full ${order.status === 'delivered' ? 'bg-green-500' : 'bg-gray-300'}`}></div>
                        <span className={`text-sm ${order.status === 'delivered' ? 'font-medium' : 'text-muted-foreground'}`}>Delivered</span>
                      </div>
                    </div>

                    {/* Delivery Notes */}
                    {order.delivery_notes && (
                      <div>
                        <h4 className="font-semibold mb-2">Delivery Notes</h4>
                        <p className="text-sm text-muted-foreground bg-muted/50 p-3 rounded">
                          {order.delivery_notes}
                        </p>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}
      </div>
    </Layout>
  );
}
