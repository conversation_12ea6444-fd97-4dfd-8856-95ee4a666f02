
import { supabase } from '@/integrations/supabase/client';
import { Order, OrderWithItems, OrderStatus, CreateOrderData, UpdateOrderData } from '@/types/order';

export const orderService = {
  async getAllOrders(): Promise<{ data: OrderWithItems[] | null; error: any }> {
    try {
      const { data, error } = await supabase
        .from('orders')
        .select(`
          *,
          profiles (
            email, full_name
          ),
          order_items (
            *,
            products (
              id, name, image_url, price
            )
          )
        `)
        .order('created_at', { ascending: false });
      
      return { data, error };
    } catch (error) {
      console.error('Error fetching all orders:', error);
      return { data: null, error };
    }
  },

  async getOrderById(id: string): Promise<{ data: OrderWithItems | null; error: any }> {
    try {
      const { data, error } = await supabase
        .from('orders')
        .select(`
          *,
          profiles (
            email, full_name
          ),
          order_items (
            *,
            products (
              id, name, image_url, price
            )
          )
        `)
        .eq('id', id)
        .single();

      return { data, error };
    } catch (error) {
      console.error('Error fetching order by ID:', error);
      return { data: null, error };
    }
  },

  async getUserOrders(userId: string): Promise<{ data: OrderWithItems[] | null; error: any }> {
    try {
      console.log('OrderService: Fetching orders for user:', userId);

      const { data, error } = await supabase
        .from('orders')
        .select(`
          *,
          profiles (
            email, full_name
          ),
          order_items (
            *,
            products (
              id, name, image_url, price
            )
          )
        `)
        .eq('user_id', userId)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Supabase error fetching user orders:', error);
        return { data: null, error };
      }

      console.log('OrderService: Successfully fetched user orders:', data?.length || 0);
      return { data, error: null };
    } catch (error) {
      console.error('Error fetching user orders:', error);
      return { data: null, error };
    }
  },

  async updateOrderStatus(id: string, status: OrderStatus): Promise<{ data: Order | null; error: any }> {
    try {
      console.log('OrderService: Updating order status', { id, status });

      const { data, error } = await supabase
        .from('orders')
        .update({
          status,
          updated_at: new Date().toISOString()
        })
        .eq('id', id)
        .select()
        .single();

      if (error) {
        console.error('Supabase error updating order status:', error);
        return { data: null, error };
      }

      console.log('OrderService: Successfully updated order status', data);
      return { data, error: null };
    } catch (error) {
      console.error('Error updating order status:', error);
      return { data: null, error };
    }
  },

  async cancelOrder(id: string): Promise<{ data: Order | null; error: any }> {
    try {
      console.log('OrderService: Cancelling order', { id });

      const { data, error } = await supabase
        .from('orders')
        .update({
          status: 'cancelled' as OrderStatus,
          updated_at: new Date().toISOString()
        })
        .eq('id', id)
        .select()
        .single();

      if (error) {
        console.error('Supabase error cancelling order:', error);
        return { data: null, error };
      }

      console.log('OrderService: Successfully cancelled order', data);
      return { data, error: null };
    } catch (error) {
      console.error('Error cancelling order:', error);
      return { data: null, error };
    }
  },

  async getOrdersByStatus(status: OrderStatus): Promise<{ data: OrderWithItems[] | null; error: any }> {
    try {
      const { data, error } = await supabase
        .from('orders')
        .select(`
          *,
          profiles (
            email, full_name
          ),
          order_items (
            *,
            products (
              id, name, image_url, price
            )
          )
        `)
        .eq('status', status)
        .order('created_at', { ascending: false });
      
      return { data, error };
    } catch (error) {
      console.error('Error fetching orders by status:', error);
      return { data: null, error };
    }
  },

  async getOrderStats(): Promise<{ data: any; error: any }> {
    try {
      const { data, error } = await supabase
        .from('orders')
        .select('status, total_amount, created_at');

      if (error) return { data: null, error };

      const stats = {
        total: data.length,
        pending: data.filter(o => o.status === 'pending').length,
        processing: data.filter(o => o.status === 'processing').length,
        shipped: data.filter(o => o.status === 'shipped').length,
        delivered: data.filter(o => o.status === 'delivered').length,
        cancelled: data.filter(o => o.status === 'cancelled').length,
        totalRevenue: data
          .filter(o => o.status === 'delivered')
          .reduce((sum, o) => sum + o.total_amount, 0)
      };

      return { data: stats, error: null };
    } catch (error) {
      console.error('Error fetching order stats:', error);
      return { data: null, error };
    }
  },

  async createOrder(orderData: CreateOrderData): Promise<{ data: Order | null; error: any }> {
    try {
      const { order_items, ...orderInfo } = orderData;

      // Create the order first
      const { data: order, error: orderError } = await supabase
        .from('orders')
        .insert([{
          ...orderInfo,
          status: orderData.status || 'pending'
        }])
        .select()
        .single();

      if (orderError) throw orderError;

      // Create order items
      if (order_items && order_items.length > 0) {
        const orderItemsData = order_items.map(item => ({
          order_id: order.id,
          product_id: item.product_id,
          quantity: item.quantity,
          price_at_time: item.price_at_time,
          unit_price: item.unit_price || item.price_at_time,
          total_price: item.total_price || (item.price_at_time * item.quantity)
        }));

        const { error: itemsError } = await supabase
          .from('order_items')
          .insert(orderItemsData);

        if (itemsError) throw itemsError;
      }

      return { data: order, error: null };
    } catch (error) {
      console.error('Error creating order:', error);
      return { data: null, error };
    }
  },

  async updateOrder(id: string, orderData: UpdateOrderData): Promise<{ data: Order | null; error: any }> {
    try {
      const updateData = {
        ...orderData,
        updated_at: new Date().toISOString()
      };

      const { data, error } = await supabase
        .from('orders')
        .update(updateData)
        .eq('id', id)
        .select()
        .single();

      return { data, error };
    } catch (error) {
      console.error('Error updating order:', error);
      return { data: null, error };
    }
  },

  async deleteOrder(id: string): Promise<{ data: any; error: any }> {
    try {
      const { data, error } = await supabase
        .from('orders')
        .delete()
        .eq('id', id);

      return { data, error };
    } catch (error) {
      console.error('Error deleting order:', error);
      return { data: null, error };
    }
  },

  async getOrdersWithItems(): Promise<{ data: OrderWithItems[] | null; error: any }> {
    // This is the same as getAllOrders since we already include items
    return this.getAllOrders();
  }
};
