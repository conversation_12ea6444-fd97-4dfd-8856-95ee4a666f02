// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = import.meta.env.VITE_SUPABASE_URL || "https://olnhzuxguxjwcbsxytju.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = import.meta.env.VITE_SUPABASE_ANON_KEY || "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im9sbmh6dXhndXhqd2Nic3h5dGp1Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg2MTc3MDEsImV4cCI6MjA2NDE5MzcwMX0.zKcLCo1k2G4pwOyV2lnfKTkPeKdMQV1W1SKd81hWTxQ";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY);