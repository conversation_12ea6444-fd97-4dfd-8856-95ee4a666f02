
import React, { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { productService } from '@/services/productService';
import { imageService } from '@/services/imageService';
import { Product, ProductCategory, ProductImage } from '@/types/product';
import { useToast } from '@/hooks/use-toast';
import { ImageUpload } from './ImageUpload';

const productSchema = z.object({
  name: z.string().min(1, 'Product name is required'),
  description: z.string().optional(),
  price: z.number().min(0, 'Price must be positive'),
  category: z.string().min(1, 'Category is required'),
  image_url: z.string().url().optional().or(z.literal('')),
  file_url: z.string().url().optional().or(z.literal('')),
  stock_quantity: z.number().min(0, 'Stock must be non-negative').optional(),
});

type ProductFormData = z.infer<typeof productSchema>;

interface ProductFormProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  product?: Product;
  onSuccess: () => void;
}

const categories: ProductCategory[] = [
  'Electronics',
  'Digital Templates',
  'Print Products',
  'Accessories',
  'Software',
  'Services',
  'PDF',
  'Book',
  'Package',
  'Course',
  'Ebook',
  'Template',
  'Tool'
];

export function ProductForm({ open, onOpenChange, product, onSuccess }: ProductFormProps) {
  const { toast } = useToast();
  const [images, setImages] = useState<ProductImage[]>([]);
  const [activeTab, setActiveTab] = useState('basic');

  const form = useForm<ProductFormData>({
    resolver: zodResolver(productSchema),
    defaultValues: {
      name: '',
      description: '',
      price: 0,
      category: '',
      image_url: '',
      file_url: '',
      stock_quantity: 0,
    },
  });

  useEffect(() => {
    if (product) {
      form.reset({
        name: product.name || '',
        description: product.description || '',
        price: product.price || 0,
        category: product.category || '',
        image_url: product.image_url || '',
        file_url: product.file_url || '',
        stock_quantity: product.stock_quantity || 0,
      });
      // Load existing images
      setImages(product.product_images || []);
    } else {
      form.reset({
        name: '',
        description: '',
        price: 0,
        category: '',
        image_url: '',
        file_url: '',
        stock_quantity: 0,
      });
      setImages([]);
    }
  }, [product, form]);

  const onSubmit = async (data: ProductFormData) => {
    try {
      console.log('Submitting product data:', data);

      let productId = product?.id;

      if (product) {
        // Update existing product
        const { error } = await productService.updateProduct(product.id, data);
        if (error) throw error;
      } else {
        // Create new product
        const { data: newProduct, error } = await productService.createProduct({
          ...data,
          is_active: true,
        });
        if (error) throw error;
        productId = newProduct?.id;
      }

      // Handle images if we have any
      if (images.length > 0 && productId) {
        // For new products, upload blob images to storage
        if (!product) {
          const blobImages = images.filter(img => img.url.startsWith('blob:'));
          if (blobImages.length > 0) {
            // Convert blob URLs back to files and upload
            const uploadPromises = blobImages.map(async (img, index) => {
              try {
                const response = await fetch(img.url);
                const blob = await response.blob();
                const file = new File([blob], img.file_name || `image-${index}.jpg`, { type: blob.type });
                const { data: uploadedUrl } = await imageService.uploadImage(file, productId!);
                return {
                  ...img,
                  url: uploadedUrl || img.url
                };
              } catch (error) {
                console.error('Error uploading image:', error);
                return img;
              }
            });

            const uploadedImages = await Promise.all(uploadPromises);
            const finalImages = [
              ...images.filter(img => !img.url.startsWith('blob:')),
              ...uploadedImages
            ];

            await imageService.updateProductImages(productId, finalImages);
          }
        } else {
          // For existing products, just update the image data
          await imageService.updateProductImages(productId, images);
        }
      }

      toast({
        title: "Success",
        description: product ? "Product updated successfully" : "Product created successfully",
      });

      onSuccess();
      onOpenChange(false);
      form.reset();
      setImages([]);
    } catch (error: any) {
      console.error('Product form error:', error);
      toast({
        title: "Error",
        description: error.message || "Failed to save product",
        variant: "destructive",
      });
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>
            {product ? 'Edit Product' : 'Add New Product'}
          </DialogTitle>
        </DialogHeader>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="basic">Basic Info</TabsTrigger>
            <TabsTrigger value="images">Images</TabsTrigger>
          </TabsList>

          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
              <TabsContent value="basic" className="space-y-4">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Product Name</FormLabel>
                  <FormControl>
                    <Input placeholder="Enter product name" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            
            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Description</FormLabel>
                  <FormControl>
                    <Textarea 
                      placeholder="Enter product description" 
                      className="resize-none"
                      {...field} 
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="price"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Price ($)</FormLabel>
                    <FormControl>
                      <Input 
                        type="number" 
                        step="0.01"
                        placeholder="0.00"
                        {...field}
                        onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="stock_quantity"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Stock Quantity</FormLabel>
                    <FormControl>
                      <Input 
                        type="number"
                        placeholder="0"
                        {...field}
                        onChange={(e) => field.onChange(parseInt(e.target.value) || 0)}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="category"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Category</FormLabel>
                  <Select onValueChange={field.onChange} value={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select a category" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {categories.map((category) => (
                        <SelectItem key={category} value={category}>
                          {category}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="image_url"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Image URL</FormLabel>
                  <FormControl>
                    <Input placeholder="https://example.com/image.jpg" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="file_url"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>File URL (for digital products)</FormLabel>
                  <FormControl>
                    <Input placeholder="https://example.com/file.pdf" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
              </TabsContent>

              <TabsContent value="images" className="space-y-4">
                <div className="space-y-4">
                  <div>
                    <h3 className="text-lg font-medium">Product Images</h3>
                    <p className="text-sm text-muted-foreground">
                      Upload images for your product. The first image will be used as the primary image.
                    </p>
                  </div>
                  <ImageUpload
                    productId={product?.id}
                    images={images}
                    onImagesChange={setImages}
                    maxImages={10}
                  />
                </div>
              </TabsContent>

              <div className="flex justify-end space-x-2 pt-4">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => onOpenChange(false)}
                >
                  Cancel
                </Button>
                <Button type="submit">
                  {product ? 'Update Product' : 'Create Product'}
                </Button>
              </div>
            </form>
          </Form>
        </Tabs>
      </DialogContent>
    </Dialog>
  );
}
