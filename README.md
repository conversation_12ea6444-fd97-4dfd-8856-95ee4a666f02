# ENZO SHOP - Premium Digital Products

A modern e-commerce platform for digital products built with React, TypeScript, and Supabase.

## Features

- 🛍️ Product browsing and search
- 🛒 Shopping cart functionality (works without login)
- 🔐 Optional authentication (only required for checkout)
- 📱 Responsive design with dark/light theme support
- 👨‍💼 Admin panel for product and order management
- 💳 Secure checkout process
- 📦 Order tracking and history
- 📱 Multiple product categories (PDF, Books, Packages, Electronics)
- 🎨 Modern UI with theme switching

## Getting Started

### Prerequisites

- Node.js (v18 or higher)
- npm or yarn

### Installation

1. Clone the repository:
```sh
git clone <YOUR_GIT_URL>
cd enzo-shop
```

2. Install dependencies:
```sh
npm install
```

3. Start the development server:
```sh
npm run dev
```

4. Open your browser and navigate to `http://localhost:8080`

## Technologies Used

This project is built with:

- **Frontend**: React 18 with TypeScript
- **Build Tool**: Vite
- **Styling**: Tailwind CSS
- **UI Components**: shadcn/ui
- **Backend**: Supabase (PostgreSQL database, Authentication, Storage)
- **State Management**: React Context API
- **Form Handling**: React Hook Form with Zod validation
- **Routing**: React Router DOM

## Project Structure

```
src/
├── components/          # Reusable UI components
│   ├── admin/          # Admin-specific components
│   ├── layout/         # Layout components (Header, Footer)
│   ├── products/       # Product-related components
│   └── ui/             # shadcn/ui components
├── contexts/           # React Context providers
├── hooks/              # Custom React hooks
├── pages/              # Page components
├── services/           # API service functions
├── types/              # TypeScript type definitions
└── lib/                # Utility functions and configurations
```

## Environment Setup

1. Create a Supabase project at [supabase.com](https://supabase.com)
2. Copy your project URL and anon key
3. Create a `.env.local` file (copy from `.env.example`):
```env
VITE_SUPABASE_URL=your_supabase_url
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key
```

## Deployment

### Deploy to Render.com

This project is configured for easy deployment to Render.com:

1. **Push your code to GitHub** (if not already done)
2. **Connect to Render**:
   - Go to [render.com](https://render.com) and sign up/login
   - Click "New +" and select "Static Site"
   - Connect your GitHub repository
3. **Configure deployment**:
   - Render will automatically detect the `render.yaml` file
   - The build command and environment variables are pre-configured
   - Click "Create Static Site"
4. **Environment Variables** (if needed):
   - The Supabase credentials are included in `render.yaml`
   - You can override them in the Render dashboard if needed

### Manual Deployment Steps

If you prefer manual configuration:

1. **Build Command**: `npm install && npm run build`
2. **Publish Directory**: `dist`
3. **Environment Variables**:
   - `VITE_SUPABASE_URL`: Your Supabase project URL
   - `VITE_SUPABASE_ANON_KEY`: Your Supabase anon key
4. **Redirects**: Configure SPA redirects (/* -> /index.html)

## Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build
- `npm run lint` - Run ESLint

## License

This project is licensed under the MIT License.
