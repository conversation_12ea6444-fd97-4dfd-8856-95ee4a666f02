
import { supabase } from '@/integrations/supabase/client';

export const cartService = {
  // Get user's cart
  async getCart(userId: string) {
    const { data, error } = await supabase
      .from('cart_items')
      .select(`
        *,
        products (
          id, name, price, image_url, is_active
        )
      `)
      .eq('user_id', userId);
    return { data, error };
  },

  // Add item to cart
  async addToCart(userId: string, productId: string, quantity: number = 1) {
    const { data, error } = await supabase
      .from('cart_items')
      .upsert({
        user_id: userId,
        product_id: productId,
        quantity
      }, {
        onConflict: 'user_id,product_id'
      })
      .select();
    return { data, error };
  },

  // Update cart item quantity
  async updateCartItem(userId: string, productId: string, quantity: number) {
    if (quantity <= 0) {
      return cartService.removeFromCart(userId, productId);
    }
    
    const { data, error } = await supabase
      .from('cart_items')
      .update({ quantity })
      .eq('user_id', userId)
      .eq('product_id', productId)
      .select();
    return { data, error };
  },

  // Remove item from cart
  async removeFromCart(userId: string, productId: string) {
    const { data, error } = await supabase
      .from('cart_items')
      .delete()
      .eq('user_id', userId)
      .eq('product_id', productId);
    return { data, error };
  },

  // Clear cart
  async clearCart(userId: string) {
    const { data, error } = await supabase
      .from('cart_items')
      .delete()
      .eq('user_id', userId);
    return { data, error };
  }
};
