
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Light mode colors */
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 98%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 98%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 98%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;

    --radius: 0.5rem;
  }

  .dark {
    /* Dark mode colors */
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
  }

  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground theme-transition;
    font-feature-settings: "rlig" 1, "calt" 1;
  }

  html {
    scroll-behavior: smooth;
  }

  /* Custom scrollbar */
  ::-webkit-scrollbar {
    width: 8px;
  }

  ::-webkit-scrollbar-track {
    @apply bg-muted;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-accent rounded-md;
  }

  ::-webkit-scrollbar-thumb:hover {
    @apply bg-accent/80;
  }
}

@layer components {
  .gradient-text {
    @apply bg-gradient-to-r from-blue-400 to-purple-600 bg-clip-text text-transparent;
  }

  /* Theme-aware gradient text */
  .light .gradient-text {
    @apply bg-gradient-to-r from-blue-600 to-purple-700 bg-clip-text text-transparent;
  }

  .dark .gradient-text {
    @apply bg-gradient-to-r from-blue-400 to-purple-500 bg-clip-text text-transparent;
  }

  .glass-effect {
    @apply bg-background/80 backdrop-blur-sm border border-border/50;
  }

  .hover-glow {
    @apply transition-all duration-300 hover:shadow-lg hover:shadow-primary/25;
  }

  /* Light mode specific styles */
  .light .glass-effect {
    @apply bg-white/80 border-gray-200/50;
  }

  .light .hover-glow {
    @apply hover:shadow-gray-400/25;
  }

  /* Dark mode specific styles */
  .dark .glass-effect {
    @apply bg-background/80 border-white/10;
  }

  .dark .hover-glow {
    @apply hover:shadow-blue-500/25;
  }

  /* Additional theme-aware styles */
  .theme-transition {
    @apply transition-colors duration-300 ease-in-out;
  }

  /* Ensure images work well in both themes */
  .theme-image {
    @apply transition-opacity duration-300;
  }

  .dark .theme-image {
    @apply opacity-90;
  }

  .light .theme-image {
    @apply opacity-100;
  }
}
