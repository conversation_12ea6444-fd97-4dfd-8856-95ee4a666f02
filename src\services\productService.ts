
import { supabase } from '@/integrations/supabase/client';
import { Product, CreateProductData, UpdateProductData } from '@/types/product';

export const productService = {
  async getAllProducts(): Promise<{ data: Product[] | null; error: any }> {
    try {
      const { data, error } = await supabase
        .from('products')
        .select('*')
        .order('created_at', { ascending: false });
      
      return { data, error };
    } catch (error) {
      console.error('Error fetching all products:', error);
      return { data: null, error };
    }
  },

  async getProductById(id: string): Promise<{ data: Product | null; error: any }> {
    try {
      const { data, error } = await supabase
        .from('products')
        .select('*')
        .eq('id', id)
        .single();
      
      return { data, error };
    } catch (error) {
      console.error('Error fetching product by ID:', error);
      return { data: null, error };
    }
  },

  async createProduct(productData: CreateProductData): Promise<{ data: Product | null; error: any }> {
    try {
      const { data, error } = await supabase
        .from('products')
        .insert([{
          name: productData.name,
          description: productData.description || null,
          price: productData.price,
          stock_quantity: productData.stock_quantity || 0,
          category: productData.category,
          image_url: productData.image_url || null,
          file_url: productData.file_url || null,
          is_active: productData.is_active !== undefined ? productData.is_active : true
        }])
        .select()
        .single();
      
      return { data, error };
    } catch (error) {
      console.error('Error creating product:', error);
      return { data: null, error };
    }
  },

  async updateProduct(id: string, productData: UpdateProductData): Promise<{ data: Product | null; error: any }> {
    try {
      const updateData = {
        ...productData,
        updated_at: new Date().toISOString()
      };

      const { data, error } = await supabase
        .from('products')
        .update(updateData)
        .eq('id', id)
        .select()
        .single();
      
      return { data, error };
    } catch (error) {
      console.error('Error updating product:', error);
      return { data: null, error };
    }
  },

  async deleteProduct(id: string): Promise<{ data: any; error: any }> {
    try {
      const { data, error } = await supabase
        .from('products')
        .delete()
        .eq('id', id);
      
      return { data, error };
    } catch (error) {
      console.error('Error deleting product:', error);
      return { data: null, error };
    }
  },

  async toggleProductStatus(id: string, isActive: boolean): Promise<{ data: Product | null; error: any }> {
    try {
      const { data, error } = await supabase
        .from('products')
        .update({ 
          is_active: isActive,
          updated_at: new Date().toISOString()
        })
        .eq('id', id)
        .select()
        .single();
      
      return { data, error };
    } catch (error) {
      console.error('Error toggling product status:', error);
      return { data: null, error };
    }
  },

  async getProductsByCategory(category: string): Promise<{ data: Product[] | null; error: any }> {
    try {
      const { data, error } = await supabase
        .from('products')
        .select('*')
        .eq('category', category)
        .eq('is_active', true)
        .order('created_at', { ascending: false });
      
      return { data, error };
    } catch (error) {
      console.error('Error fetching products by category:', error);
      return { data: null, error };
    }
  },

  async updateProductStock(id: string, quantity: number): Promise<{ data: Product | null; error: any }> {
    try {
      const { data, error } = await supabase
        .from('products')
        .update({ 
          stock_quantity: quantity,
          updated_at: new Date().toISOString()
        })
        .eq('id', id)
        .select()
        .single();
      
      return { data, error };
    } catch (error) {
      console.error('Error updating product stock:', error);
      return { data: null, error };
    }
  }
};
